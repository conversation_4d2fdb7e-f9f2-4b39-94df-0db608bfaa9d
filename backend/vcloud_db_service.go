package backend

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/virtualeconomy/go-vgraph/rpc"
)

const (
	// VCloudDBContractAddress VCloud DB合约地址
	VCloudDBContractAddress = "0xab6492e2c4bf3d33f2805b164b130fed1f97bff0fb5b30f3e2240979268bd553"
	// DefaultRPCURL 默认RPC连接地址
	DefaultRPCURL = "682afc8eecab37cdeffac5f7.europe.test.vkube.vcloud.systems:9877"
)

// VCloudDBService 结构体，用于封装与VCloud DB合约的交互
type VCloudDBService struct {
	ctx    context.Context
	client *rpc.Client
	rpcURL string
}

// NewVCloudDBService 创建并初始化 VCloudDBService
func NewVCloudDBService() *VCloudDBService {
	return &VCloudDBService{
		rpcURL: DefaultRPCURL,
	}
}

// WailsInit 是 Wails 生命周期方法，在应用启动时调用
func (s *VCloudDBService) WailsInit(ctx context.Context) error {
	s.ctx = ctx

	// 连接到VGraph节点
	client, err := rpc.Connect(s.rpcURL)
	if err != nil {
		log.Printf("Failed to connect to VGraph node: %v", err)
		return fmt.Errorf("failed to connect to VGraph node: %w", err)
	}
	s.client = client

	log.Printf("Successfully connected to VGraph node at %s", s.rpcURL)
	return nil
}

// WailsShutdown 是 Wails 生命周期方法，在应用关闭时调用
func (s *VCloudDBService) WailsShutdown(ctx context.Context) {
	if s.client != nil {
		s.client.Close()
		log.Println("VGraph RPC client connection closed")
	}
}

// FindRecords Go 方法，暴露给前端调用
// tableName: 表名
// filter: 查询过滤条件，将从前端的 JavaScript 对象自动转换为 Go 的 map[string]interface{}
func (s *VCloudDBService) FindRecords(tableName string, filter map[string]interface{}) ([]json.RawMessage, error) {
	if s.client == nil {
		return nil, fmt.Errorf("RPC client not initialized")
	}

	filterBytes, err := json.Marshal(filter)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal filter: %w", err)
	}

	queryParams := rpc.QueryContractParams{
		ContractAddress: VCloudDBContractAddress,
		FunctionName:    "find",
		Args:            []interface{}{tableName, string(filterBytes)},
	}

	result, err := s.client.QueryContract(s.ctx, queryParams)
	if err != nil {
		return nil, fmt.Errorf("failed to query contract: %w", err)
	}

	var records []json.RawMessage
	if err := json.Unmarshal(result, &records); err != nil {
		return nil, fmt.Errorf("failed to unmarshal query result: %w", err)
	}

	log.Printf("Found %d records in table %s", len(records), tableName)
	return records, nil
}

// GetRecordById Go 方法，暴露给前端调用
// tableName: 表名
// id: 记录ID
func (s *VCloudDBService) GetRecordById(tableName string, id string) (json.RawMessage, error) {
	if s.client == nil {
		return nil, fmt.Errorf("RPC client not initialized")
	}

	filter := map[string]string{"_id": id}
	filterBytes, err := json.Marshal(filter)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal filter for get: %w", err)
	}

	queryParams := rpc.QueryContractParams{
		ContractAddress: VCloudDBContractAddress,
		FunctionName:    "get",
		Args:            []interface{}{tableName, string(filterBytes)},
	}

	result, err := s.client.QueryContract(s.ctx, queryParams)
	if err != nil {
		return nil, fmt.Errorf("failed to query contract for get: %w", err)
	}

	log.Printf("Retrieved record %s from table %s", id, tableName)
	return result, nil
}

// CountRecords Go 方法，暴露给前端调用
// tableName: 表名
// filter: 查询过滤条件
func (s *VCloudDBService) CountRecords(tableName string, filter map[string]interface{}) (int, error) {
	if s.client == nil {
		return 0, fmt.Errorf("RPC client not initialized")
	}

	filterBytes, err := json.Marshal(filter)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal filter: %w", err)
	}

	queryParams := rpc.QueryContractParams{
		ContractAddress: VCloudDBContractAddress,
		FunctionName:    "count",
		Args:            []interface{}{tableName, string(filterBytes)},
	}

	result, err := s.client.QueryContract(s.ctx, queryParams)
	if err != nil {
		return 0, fmt.Errorf("failed to count records: %w", err)
	}

	var count int
	if err := json.Unmarshal(result, &count); err != nil {
		return 0, fmt.Errorf("failed to unmarshal count result: %w", err)
	}

	log.Printf("Counted %d records in table %s", count, tableName)
	return count, nil
}

// GetConnectionStatus 获取连接状态
func (s *VCloudDBService) GetConnectionStatus() map[string]interface{} {
	status := map[string]interface{}{
		"connected": s.client != nil,
		"rpcURL":    s.rpcURL,
	}

	if s.client != nil {
		status["status"] = "Connected"
	} else {
		status["status"] = "Disconnected"
	}

	return status
}

// SetRPCURL 设置RPC连接地址
func (s *VCloudDBService) SetRPCURL(url string) error {
	if url == "" {
		return fmt.Errorf("RPC URL cannot be empty")
	}

	// 如果已有连接，先关闭
	if s.client != nil {
		s.client.Close()
		s.client = nil
	}

	s.rpcURL = url

	// 尝试连接新地址
	client, err := rpc.Connect(s.rpcURL)
	if err != nil {
		return fmt.Errorf("failed to connect to new RPC URL: %w", err)
	}

	s.client = client
	log.Printf("Successfully connected to new RPC URL: %s", s.rpcURL)
	return nil
}
