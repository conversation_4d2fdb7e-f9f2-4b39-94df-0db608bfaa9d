package backend

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/virtualeconomy/go-vgraph/rpc"
)

// SimpleVCloudDBService 简化版的VCloud DB服务，不依赖Wails
type SimpleVCloudDBService struct {
	ctx    context.Context
	client *rpc.Client
	rpcURL string
}

// NewSimpleVCloudDBService 创建简化版服务
func NewSimpleVCloudDBService() *SimpleVCloudDBService {
	return &SimpleVCloudDBService{
		rpcURL: DefaultRPCURL,
	}
}

// Init 初始化服务
func (s *SimpleVCloudDBService) Init(ctx context.Context) error {
	s.ctx = ctx
	
	// 连接到VGraph节点
	client, err := rpc.Connect(s.rpcURL)
	if err != nil {
		log.Printf("Failed to connect to VGraph node: %v", err)
		return fmt.Errorf("failed to connect to VGraph node: %w", err)
	}
	s.client = client
	
	log.Printf("Successfully connected to VGraph node at %s", s.rpcURL)
	return nil
}

// Close 关闭服务
func (s *SimpleVCloudDBService) Close() {
	if s.client != nil {
		s.client.Close()
		log.Println("VGraph RPC client connection closed")
	}
}

// FindRecords 查询记录
func (s *SimpleVCloudDBService) FindRecords(tableName string, filter map[string]interface{}) ([]json.RawMessage, error) {
	if s.client == nil {
		return nil, fmt.Errorf("RPC client not initialized")
	}

	filterBytes, err := json.Marshal(filter)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal filter: %w", err)
	}

	queryParams := rpc.QueryContractParams{
		ContractAddress: VCloudDBContractAddress,
		FunctionName:    "find",
		Args:            []interface{}{tableName, string(filterBytes)},
	}

	result, err := s.client.QueryContract(s.ctx, queryParams)
	if err != nil {
		return nil, fmt.Errorf("failed to query contract: %w", err)
	}

	var records []json.RawMessage
	if err := json.Unmarshal(result, &records); err != nil {
		return nil, fmt.Errorf("failed to unmarshal query result: %w", err)
	}
	
	log.Printf("Found %d records in table %s", len(records), tableName)
	return records, nil
}

// GetRecordById 根据ID获取记录
func (s *SimpleVCloudDBService) GetRecordById(tableName string, id string) (json.RawMessage, error) {
	if s.client == nil {
		return nil, fmt.Errorf("RPC client not initialized")
	}

	filter := map[string]string{"_id": id}
	filterBytes, err := json.Marshal(filter)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal filter for get: %w", err)
	}

	queryParams := rpc.QueryContractParams{
		ContractAddress: VCloudDBContractAddress,
		FunctionName:    "get",
		Args:            []interface{}{tableName, string(filterBytes)},
	}

	result, err := s.client.QueryContract(s.ctx, queryParams)
	if err != nil {
		return nil, fmt.Errorf("failed to query contract for get: %w", err)
	}
	
	log.Printf("Retrieved record %s from table %s", id, tableName)
	return result, nil
}

// CountRecords 计数记录
func (s *SimpleVCloudDBService) CountRecords(tableName string, filter map[string]interface{}) (int, error) {
	if s.client == nil {
		return 0, fmt.Errorf("RPC client not initialized")
	}

	filterBytes, err := json.Marshal(filter)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal filter: %w", err)
	}

	queryParams := rpc.QueryContractParams{
		ContractAddress: VCloudDBContractAddress,
		FunctionName:    "count",
		Args:            []interface{}{tableName, string(filterBytes)},
	}

	result, err := s.client.QueryContract(s.ctx, queryParams)
	if err != nil {
		return 0, fmt.Errorf("failed to count records: %w", err)
	}

	var count int
	if err := json.Unmarshal(result, &count); err != nil {
		return 0, fmt.Errorf("failed to unmarshal count result: %w", err)
	}
	
	log.Printf("Counted %d records in table %s", count, tableName)
	return count, nil
}

// GetConnectionStatus 获取连接状态
func (s *SimpleVCloudDBService) GetConnectionStatus() map[string]interface{} {
	status := map[string]interface{}{
		"connected": s.client != nil,
		"rpcURL":    s.rpcURL,
	}
	
	if s.client != nil {
		status["status"] = "Connected"
	} else {
		status["status"] = "Disconnected"
	}
	
	return status
}
