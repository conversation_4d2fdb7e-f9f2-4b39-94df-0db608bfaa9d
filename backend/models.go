package backend

// 数据模型定义，基于 vcloud_db_tables_summary.md

// UserService 用户服务表模型
type UserService struct {
	ID                   string            `json:"_id"`
	Duration             int64             `json:"duration"`
	Amount               float64           `json:"amount"`
	PublicKey            string            `json:"publicKey"`
	Provider             string            `json:"provider"`
	ProviderAddress      string            `json:"providerAddress"`
	Address              string            `json:"address"`
	ServiceID            string            `json:"serviceID"`
	ServiceActivated     bool              `json:"serviceActivated"`
	Status               string            `json:"status"`
	ServiceOptions       map[string]string `json:"serviceOptions"`
	CreatedAt            int64             `json:"createdAt"`
	UpdatedAt            int64             `json:"updatedAt"`
	DeletedAt            int64             `json:"deletedAt"`
	EndAt                int64             `json:"endAt"`
	ServiceActivateTS    int64             `json:"serviceActivateTS"`
	ServiceRunningTS     int64             `json:"serviceRunningTS"`
	ServiceAbortTS       int64             `json:"serviceAbortTS"`
	ServiceDoneTS        int64             `json:"serviceDoneTS"`
	ServiceRefundTS      int64             `json:"serviceRefundTS"`
	Service              string            `json:"service"`
	CreatedAddr          string            `json:"createdAddr"`
	LabelHash            string            `json:"labelHash"`
}

// Order 订单表模型
type Order struct {
	ID             string                    `json:"_id"`
	CreatedAt      int64                     `json:"createdAt"`
	UpdatedAt      int64                     `json:"updatedAt"`
	DeletedAt      int64                     `json:"deletedAt"`
	Type           string                    `json:"type"`
	Amount         float64                   `json:"amount"`
	AmountPaid     float64                   `json:"amountPaid"`
	Provider       string                    `json:"provider"`
	Address        string                    `json:"address"`
	Recipient      string                    `json:"recipient"`
	Status         string                    `json:"status"`
	LastPaymentTS  int64                     `json:"lastPaymentTS"`
	PaidTS         int64                     `json:"paidTS"`
	FiledTS        int64                     `json:"filedTS"`
	PublicKey      string                    `json:"publicKey"`
	UserServiceIDs []string                  `json:"userServiceIDs"`
	Items          []SingleServiceBriefInfo  `json:"items"`
}

// SingleServiceBriefInfo 订单项目信息
type SingleServiceBriefInfo struct {
	UserServiceID string  `json:"userServiceID"`
	Duration      int64   `json:"duration"`
	Amount        float64 `json:"amount"`
}

// OrderService 订单服务关联表模型
type OrderService struct {
	ID            string `json:"_id"`
	CreatedAt     int64  `json:"createdAt"`
	UpdatedAt     int64  `json:"updatedAt"`
	DeletedAt     int64  `json:"deletedAt"`
	OrderID       string `json:"orderID"`
	UserServiceID string `json:"userServiceID"`
	OrderStatus   string `json:"orderStatus"`
	OrderType     string `json:"orderType"`
}

// CliVersion CLI版本表模型
type CliVersion struct {
	ID               string `json:"_id"`
	Version          string `json:"version"`
	CreatedAt        int64  `json:"createdAt"`
	UpdatedAt        int64  `json:"updatedAt"`
	DeletedAt        int64  `json:"deletedAt"`
	ChangeLog        string `json:"changeLog"`
	MinimalSupported string `json:"minimalSupported"`
}

// Currency 货币表模型
type Currency struct {
	ID           string  `json:"_id"`
	CreatedAt    int64   `json:"createdAt"`
	UpdatedAt    int64   `json:"updatedAt"`
	DeletedAt    int64   `json:"deletedAt"`
	NameOrId     string  `json:"nameOrId"`
	ContractId   string  `json:"contractId"`
	SymbolName   string  `json:"symbolName"`
	ContractType string  `json:"contractType"`
	Unit         int32   `json:"unit"`
	ExchangeRate float64 `json:"exchangeRate"`
}

// ServiceCategory 服务分类表模型
type ServiceCategory struct {
	ID             string                       `json:"_id"`
	CreatedAt      int64                        `json:"createdAt"`
	UpdatedAt      int64                        `json:"updatedAt"`
	DeletedAt      int64                        `json:"deletedAt"`
	Provider       string                       `json:"provider"`
	Name           string                       `json:"name"`
	ServiceOptions map[string][]string          `json:"serviceOptions"`
	Description    string                       `json:"description"`
	Name2ID        map[string]string            `json:"name2ID"`
	ApiHost        string                       `json:"apiHost"`
}

// Provider 服务提供商表模型
type Provider struct {
	ID            string            `json:"_id"`
	CreatedAt     int64             `json:"createdAt"`
	UpdatedAt     int64             `json:"updatedAt"`
	DeletedAt     int64             `json:"deletedAt"`
	Name          string            `json:"name"`
	WalletAddress string            `json:"walletAddress"`
	PublicKey     string            `json:"publickey"`
	Category2ID   map[string]string `json:"category2ID"`
	SignAddress   string            `json:"signAddress"`
	ApiHost       string            `json:"apiHost"`
}

// ServiceType 服务类型表模型
type ServiceType struct {
	ID                 string                              `json:"_id"`
	CreatedAt          int64                               `json:"createdAt"`
	UpdatedAt          int64                               `json:"updatedAt"`
	DeletedAt          int64                               `json:"deletedAt"`
	Name               string                              `json:"name"`
	Provider           string                              `json:"provider"`
	Refundable         bool                                `json:"refundable"`
	CategoryID         string                              `json:"categoryID"`
	Category           string                              `json:"category"`
	ServiceOptions     map[string][]string                 `json:"serviceOptions"`
	Description        string                              `json:"description"`
	ApiHost            string                              `json:"apiHost"`
	DurationToPrice    []PriceSet                          `json:"durationToPrice"`
	ServiceOptionDesc  map[string]map[string]string        `json:"serviceOptionDesc"`
}

// PriceSet 价格设置
type PriceSet struct {
	Duration int64   `json:"duration"`
	Price    float64 `json:"price"`
}

// 查询参数结构定义

// BaseQueryParams 通用查询参数
type BaseQueryParams struct {
	IDs            []string `json:"ids,omitempty"`
	CreatedAtStart int64    `json:"createdAtStart,omitempty"`
	CreatedAtEnd   int64    `json:"createdAtEnd,omitempty"`
	UpdatedAtStart int64    `json:"updatedAtStart,omitempty"`
	UpdatedAtEnd   int64    `json:"updatedAtEnd,omitempty"`
	Offset         int      `json:"offset,omitempty"`
	Limit          int      `json:"limit,omitempty"`
	SortBy         string   `json:"sortBy,omitempty"`
	SortDesc       bool     `json:"sortDesc,omitempty"`
}

// UserServiceQueryParams 用户服务查询参数
type UserServiceQueryParams struct {
	BaseQueryParams
	ServiceID        string `json:"serviceID,omitempty"`
	Address          string `json:"address,omitempty"`
	Provider         string `json:"provider,omitempty"`
	ProviderAddress  string `json:"providerAddress,omitempty"`
	Status           string `json:"status,omitempty"`
	ServiceActivated *bool  `json:"serviceActivated,omitempty"`
}

// OrderQueryParams 订单查询参数
type OrderQueryParams struct {
	BaseQueryParams
	ServiceID string   `json:"serviceID,omitempty"`
	Service   string   `json:"service,omitempty"`
	Address   string   `json:"address,omitempty"`
	Recipient string   `json:"recipient,omitempty"`
	Type      string   `json:"type,omitempty"`
	Statuses  []string `json:"statuses,omitempty"`
	TSStart   int64    `json:"tsStart,omitempty"`
	TSEnd     int64    `json:"tsEnd,omitempty"`
}

// CliVersionQueryParams CLI版本查询参数
type CliVersionQueryParams struct {
	BaseQueryParams
	Version          string `json:"version,omitempty"`
	MinimalSupported string `json:"minimalSupported,omitempty"`
}

// CurrencyQueryParams 货币查询参数
type CurrencyQueryParams struct {
	BaseQueryParams
	NameOrId     string `json:"nameOrId,omitempty"`
	ContractId   string `json:"contractId,omitempty"`
	SymbolName   string `json:"symbolName,omitempty"`
	ContractType string `json:"contractType,omitempty"`
}

// ServiceCategoryQueryParams 服务分类查询参数
type ServiceCategoryQueryParams struct {
	BaseQueryParams
	Provider string `json:"provider,omitempty"`
	Name     string `json:"name,omitempty"`
}

// ProviderQueryParams 提供商查询参数
type ProviderQueryParams struct {
	BaseQueryParams
	Name          string `json:"name,omitempty"`
	WalletAddress string `json:"walletAddress,omitempty"`
	PublicKey     string `json:"publickey,omitempty"`
	SignAddress   string `json:"signAddress,omitempty"`
	ApiHost       string `json:"apiHost,omitempty"`
}

// OrderServiceQueryParams 订单服务查询参数
type OrderServiceQueryParams struct {
	BaseQueryParams
	OrderID       string `json:"orderID,omitempty"`
	UserServiceID string `json:"userServiceID,omitempty"`
	OrderStatus   string `json:"orderStatus,omitempty"`
	OrderType     string `json:"orderType,omitempty"`
}

// ServiceTypeQueryParams 服务类型查询参数
type ServiceTypeQueryParams struct {
	BaseQueryParams
	Name       string `json:"name,omitempty"`
	Provider   string `json:"provider,omitempty"`
	Category   string `json:"category,omitempty"`
	CategoryID string `json:"categoryID,omitempty"`
	Refundable *bool  `json:"refundable,omitempty"`
}
