package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"vcloud-db-ui/backend"
)

func main() {
	fmt.Println("Testing VCloud DB Backend Service...")

	// 创建服务实例
	service := backend.NewSimpleVCloudDBService()

	// 初始化服务
	ctx := context.Background()
	err := service.Init(ctx)
	if err != nil {
		log.Fatalf("Failed to initialize service: %v", err)
	}
	defer service.Close()

	// 测试连接状态
	status := service.GetConnectionStatus()
	statusJSON, _ := json.MarshalIndent(status, "", "  ")
	fmt.Printf("Connection Status:\n%s\n\n", statusJSON)

	// 测试查询订单数据
	fmt.Println("Testing FindRecords for 'order' table...")
	filter := map[string]interface{}{
		"limit":  5,
		"offset": 0,
	}

	records, err := service.FindRecords("order", filter)
	if err != nil {
		log.Printf("Error querying orders: %v", err)
	} else {
		fmt.Printf("Found %d order records\n", len(records))
		if len(records) > 0 {
			// 打印第一条记录
			var firstRecord map[string]interface{}
			if err := json.Unmarshal(records[0], &firstRecord); err == nil {
				recordJSON, _ := json.MarshalIndent(firstRecord, "", "  ")
				fmt.Printf("First record:\n%s\n", recordJSON)
			}
		}
	}

	// 测试计数功能
	fmt.Println("\nTesting CountRecords for 'order' table...")
	count, err := service.CountRecords("order", map[string]interface{}{})
	if err != nil {
		log.Printf("Error counting orders: %v", err)
	} else {
		fmt.Printf("Total order records: %d\n", count)
	}

	// 测试用户服务数据
	fmt.Println("\nTesting FindRecords for 'user_service' table...")
	userServiceFilter := map[string]interface{}{
		"limit":  3,
		"offset": 0,
	}

	userRecords, err := service.FindRecords("user_service", userServiceFilter)
	if err != nil {
		log.Printf("Error querying user services: %v", err)
	} else {
		fmt.Printf("Found %d user service records\n", len(userRecords))
	}

	fmt.Println("\nBackend service test completed!")
}
