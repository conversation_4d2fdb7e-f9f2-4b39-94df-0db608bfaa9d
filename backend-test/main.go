package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/virtualeconomy/go-vgraph/rpc"
)

const (
	VCloudDBContractAddress = "0xab6492e2c4bf3d33f2805b164b130fed1f97bff0fb5b30f3e2240979268bd553"
	DefaultRPCURL           = "682afc8eecab37cdeffac5f7.europe.test.vkube.vcloud.systems:9877"
)

// SimpleVCloudDBService 简化版的VCloud DB服务
type SimpleVCloudDBService struct {
	ctx    context.Context
	client *rpc.VGraphClient
	rpcURL string
}

// NewSimpleVCloudDBService 创建简化版服务
func NewSimpleVCloudDBService() *SimpleVCloudDBService {
	return &SimpleVCloudDBService{
		rpcURL: DefaultRPCURL,
	}
}

// Init 初始化服务
func (s *SimpleVCloudDBService) Init(ctx context.Context) error {
	s.ctx = ctx

	// 连接到VGraph节点
	client, err := rpc.Connect(s.rpcURL)
	if err != nil {
		log.Printf("Failed to connect to VGraph node: %v", err)
		return fmt.Errorf("failed to connect to VGraph node: %w", err)
	}
	s.client = client

	log.Printf("Successfully connected to VGraph node at %s", s.rpcURL)
	return nil
}

// Close 关闭服务
func (s *SimpleVCloudDBService) Close() {
	if s.client != nil {
		s.client.Close()
		log.Println("VGraph RPC client connection closed")
	}
}

// FindRecords 查询记录
func (s *SimpleVCloudDBService) FindRecords(tableName string, filter map[string]interface{}) ([]json.RawMessage, error) {
	if s.client == nil {
		return nil, fmt.Errorf("RPC client not initialized")
	}

	filterBytes, err := json.Marshal(filter)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal filter: %w", err)
	}

	queryParams := rpc.QueryContractParams{
		ContractAddress: VCloudDBContractAddress,
		FunctionName:    "find",
		Args:            []interface{}{tableName, string(filterBytes)},
	}

	result, err := s.client.QueryContract(s.ctx, queryParams)
	if err != nil {
		return nil, fmt.Errorf("failed to query contract: %w", err)
	}

	// 先打印原始结果来调试
	log.Printf("Raw result for table %s: %s", tableName, string(result))

	var records []json.RawMessage
	if err := json.Unmarshal(result, &records); err != nil {
		// 如果不是数组，尝试解析为单个对象
		var singleRecord json.RawMessage
		if err2 := json.Unmarshal(result, &singleRecord); err2 == nil {
			return []json.RawMessage{singleRecord}, nil
		}
		return nil, fmt.Errorf("failed to unmarshal query result: %w", err)
	}

	log.Printf("Found %d records in table %s", len(records), tableName)
	return records, nil
}

// CountRecords 计数记录
func (s *SimpleVCloudDBService) CountRecords(tableName string, filter map[string]interface{}) (int, error) {
	if s.client == nil {
		return 0, fmt.Errorf("RPC client not initialized")
	}

	filterBytes, err := json.Marshal(filter)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal filter: %w", err)
	}

	queryParams := rpc.QueryContractParams{
		ContractAddress: VCloudDBContractAddress,
		FunctionName:    "count",
		Args:            []interface{}{tableName, string(filterBytes)},
	}

	result, err := s.client.QueryContract(s.ctx, queryParams)
	if err != nil {
		return 0, fmt.Errorf("failed to count records: %w", err)
	}

	// 先打印原始结果来调试
	log.Printf("Raw count result for table %s: %s", tableName, string(result))

	var count int
	if err := json.Unmarshal(result, &count); err != nil {
		return 0, fmt.Errorf("failed to unmarshal count result: %w", err)
	}

	log.Printf("Counted %d records in table %s", count, tableName)
	return count, nil
}

func main() {
	fmt.Println("Testing VCloud DB Backend Service...")

	// 创建服务实例
	service := NewSimpleVCloudDBService()

	// 初始化服务
	ctx := context.Background()
	err := service.Init(ctx)
	if err != nil {
		log.Fatalf("Failed to initialize service: %v", err)
	}
	defer service.Close()

	// 测试查询订单数据
	fmt.Println("Testing FindRecords for 'order' table...")
	filter := map[string]interface{}{
		"limit":  5,
		"offset": 0,
	}

	records, err := service.FindRecords("order", filter)
	if err != nil {
		log.Printf("Error querying orders: %v", err)
	} else {
		fmt.Printf("Found %d order records\n", len(records))
		if len(records) > 0 {
			// 打印第一条记录
			var firstRecord map[string]interface{}
			if err := json.Unmarshal(records[0], &firstRecord); err == nil {
				recordJSON, _ := json.MarshalIndent(firstRecord, "", "  ")
				fmt.Printf("First record:\n%s\n", recordJSON)
			}
		}
	}

	// 测试计数功能
	fmt.Println("\nTesting CountRecords for 'order' table...")
	count, err := service.CountRecords("order", map[string]interface{}{})
	if err != nil {
		log.Printf("Error counting orders: %v", err)
	} else {
		fmt.Printf("Total order records: %d\n", count)
	}

	// 测试用户服务数据
	fmt.Println("\nTesting FindRecords for 'user_service' table...")
	userServiceFilter := map[string]interface{}{
		"limit":  3,
		"offset": 0,
	}

	userRecords, err := service.FindRecords("user_service", userServiceFilter)
	if err != nil {
		log.Printf("Error querying user services: %v", err)
	} else {
		fmt.Printf("Found %d user service records\n", len(userRecords))
	}

	// 测试其他表
	tables := []string{"cli_version", "currency", "service_category", "provider", "service_type", "order_service"}
	for _, table := range tables {
		fmt.Printf("\nTesting table '%s'...\n", table)
		tableFilter := map[string]interface{}{
			"limit": 2,
		}

		tableRecords, err := service.FindRecords(table, tableFilter)
		if err != nil {
			log.Printf("Error querying %s: %v", table, err)
		} else {
			fmt.Printf("Found %d records in %s table\n", len(tableRecords), table)
		}
	}

	fmt.Println("\nBackend service test completed!")
}
