package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/virtualeconomy/go-vgraph/rpc"
)

const (
	VCloudDBContractAddress = "0xab6492e2c4bf3d33f2805b164b130fed1f97bff0fb5b30f3e2240979268bd553"
	DefaultRPCURL = "682afc8eecab37cdeffac5f7.europe.test.vkube.vcloud.systems:9877"
)

func main() {
	fmt.Println("Testing VCloud DB RPC Connection...")
	
	// 连接到VGraph节点
	client, err := rpc.Connect(DefaultRPCURL)
	if err != nil {
		log.Fatalf("Failed to connect to VGraph node: %v", err)
	}
	defer client.Close()
	
	fmt.Printf("Successfully connected to VGraph node at %s\n", DefaultRPCURL)
	
	// 测试查询订单数据
	fmt.Println("\nTesting query for 'order' table...")
	filter := map[string]interface{}{
		"limit":  5,
		"offset": 0,
	}
	
	filterBytes, err := json.Marshal(filter)
	if err != nil {
		log.Fatalf("Failed to marshal filter: %v", err)
	}

	queryParams := rpc.QueryContractParams{
		ContractAddress: VCloudDBContractAddress,
		FunctionName:    "find",
		Args:            []interface{}{"order", string(filterBytes)},
	}

	ctx := context.Background()
	result, err := client.QueryContract(ctx, queryParams)
	if err != nil {
		log.Printf("Error querying orders: %v", err)
	} else {
		var records []json.RawMessage
		if err := json.Unmarshal(result, &records); err != nil {
			log.Printf("Error unmarshaling result: %v", err)
		} else {
			fmt.Printf("Found %d order records\n", len(records))
			if len(records) > 0 {
				// 打印第一条记录
				var firstRecord map[string]interface{}
				if err := json.Unmarshal(records[0], &firstRecord); err == nil {
					recordJSON, _ := json.MarshalIndent(firstRecord, "", "  ")
					fmt.Printf("First record:\n%s\n", recordJSON)
				}
			}
		}
	}
	
	// 测试计数功能
	fmt.Println("\nTesting count for 'order' table...")
	countParams := rpc.QueryContractParams{
		ContractAddress: VCloudDBContractAddress,
		FunctionName:    "count",
		Args:            []interface{}{"order", "{}"},
	}
	
	countResult, err := client.QueryContract(ctx, countParams)
	if err != nil {
		log.Printf("Error counting orders: %v", err)
	} else {
		var count int
		if err := json.Unmarshal(countResult, &count); err != nil {
			log.Printf("Error unmarshaling count: %v", err)
		} else {
			fmt.Printf("Total order records: %d\n", count)
		}
	}
	
	fmt.Println("\nRPC connection test completed!")
}
