{"name": "rollup", "version": "2.79.2", "description": "Next-generation ES module bundler", "main": "dist/rollup.js", "module": "dist/es/rollup.js", "typings": "dist/rollup.d.ts", "bin": {"rollup": "dist/bin/rollup"}, "scripts": {"build": "shx rm -rf dist && node scripts/update-git-commit.js && rollup --config rollup.config.ts --configPlugin typescript && shx cp src/rollup/types.d.ts dist/rollup.d.ts && shx chmod a+x dist/bin/rollup", "build:cjs": "shx rm -rf dist && rollup --config rollup.config.ts --configPlugin typescript --configTest && shx cp src/rollup/types.d.ts dist/rollup.d.ts && shx chmod a+x dist/bin/rollup", "build:bootstrap": "node dist/bin/rollup --config rollup.config.ts --configPlugin typescript && shx cp src/rollup/types.d.ts dist/rollup.d.ts && shx chmod a+x dist/bin/rollup && cp -r dist browser/", "ci:lint": "npm run lint:nofix", "ci:test": "npm run build:cjs && npm run build:bootstrap && npm run test:all", "ci:test:only": "npm run build:cjs && npm run build:bootstrap && npm run test:only", "ci:coverage": "npm run build:cjs && npm run build:bootstrap && nyc --reporter l<PERSON><PERSON><PERSON> mocha", "lint": "eslint . --fix --cache && prettier --write \"**/*.md\"", "lint:nofix": "eslint . --cache && prettier --check \"**/*.md\"", "lint:markdown": "prettier --write \"**/*.md\"", "perf": "npm run build:cjs && node --expose-gc scripts/perf.js", "perf:debug": "node --inspect-brk scripts/perf-debug.js", "perf:init": "node scripts/perf-init.js", "postpublish": "git push && git push --tags", "security": "npm audit", "test": "npm run build && npm run test:all", "test:cjs": "npm run build:cjs && npm run test:only", "test:quick": "mocha -b test/test.js", "test:all": "npm run test:only && npm run test:browser && npm run test:typescript && npm run test:leak && npm run test:package", "test:coverage": "npm run build:cjs && shx rm -rf coverage/* && nyc --reporter html mocha test/test.js", "test:coverage:browser": "npm run build && shx rm -rf coverage/* && nyc mocha test/browser/index.js", "test:leak": "node --expose-gc test/leak/index.js", "test:package": "node scripts/test-package.js", "test:only": "mocha test/test.js", "test:typescript": "shx rm -rf test/typescript/dist && shx cp -r dist test/typescript/ && tsc --noEmit -p test/typescript && tsc --noEmit", "test:browser": "mocha test/browser/index.js", "watch": "rollup --config rollup.config.ts --configPlugin typescript --watch"}, "repository": "rollup/rollup", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "homepage": "https://rollupjs.org/", "optionalDependencies": {"fsevents": "~2.3.2"}, "devDependencies": {"@rollup/plugin-alias": "^3.1.9", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-commonjs": "^22.0.1", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.3.0", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.3.3", "@rollup/pluginutils": "^4.2.1", "@types/estree": "0.0.52", "@types/node": "^10.17.60", "@types/signal-exit": "^3.0.1", "@types/yargs-parser": "^20.2.2", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "^5.30.7", "acorn": "^8.7.1", "acorn-jsx": "^5.3.2", "acorn-walk": "^8.2.0", "buble": "^0.20.0", "chokidar": "^3.5.3", "colorette": "^2.0.19", "core-js": "^3.23.5", "date-time": "^4.0.0", "es5-shim": "^4.6.7", "es6-shim": "^0.35.6", "eslint": "^8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "execa": "^5.1.1", "fixturify": "^2.1.1", "fs-extra": "^10.1.0", "hash.js": "^1.1.7", "husky": "^7.0.4", "is-reference": "^3.0.0", "lint-staged": "^10.5.4", "locate-character": "^2.0.5", "magic-string": "^0.26.2", "mocha": "^9.2.2", "nyc": "^15.1.0", "prettier": "^2.7.1", "pretty-bytes": "^5.6.0", "pretty-ms": "^7.0.1", "requirejs": "^2.3.6", "rollup": "^2.77.0", "rollup-plugin-license": "^2.8.1", "rollup-plugin-string": "^3.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-thatworks": "^1.0.4", "shx": "^0.3.4", "signal-exit": "^3.0.7", "source-map": "^0.7.4", "source-map-support": "^0.5.21", "sourcemap-codec": "^1.4.8", "systemjs": "^6.12.1", "terser": "^5.14.2", "tslib": "^2.4.0", "typescript": "^4.7.4", "weak-napi": "^2.0.2", "yargs-parser": "^20.2.9"}, "files": ["dist/**/*.js", "dist/*.d.ts", "dist/bin/rollup", "dist/es/package.json", "dist/rollup.browser.js.map"], "engines": {"node": ">=10.0.0"}, "exports": {".": {"types": "./dist/rollup.d.ts", "node": {"require": "./dist/rollup.js", "import": "./dist/es/rollup.js"}, "default": "./dist/es/rollup.browser.js"}, "./loadConfigFile": "./dist/loadConfigFile.js", "./dist/loadConfigFile": "./dist/loadConfigFile.js", "./dist/*": "./dist/*"}}