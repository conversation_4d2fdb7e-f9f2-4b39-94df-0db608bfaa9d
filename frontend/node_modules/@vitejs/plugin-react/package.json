{"name": "@vitejs/plugin-react", "version": "2.2.0", "license": "MIT", "author": "<PERSON>", "contributors": ["<PERSON>"], "files": ["dist"], "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "scripts": {"dev": "unbuild --stub", "build": "unbuild && pnpm run patch-cjs", "patch-cjs": "tsx ../../scripts/patchCJS.ts", "prepublishOnly": "npm run build"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/plugin-react"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/packages/plugin-react#readme", "dependencies": {"@babel/core": "^7.19.6", "@babel/plugin-transform-react-jsx": "^7.19.0", "@babel/plugin-transform-react-jsx-development": "^7.18.6", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.19.6", "magic-string": "^0.26.7", "react-refresh": "^0.14.0"}, "peerDependencies": {"vite": "^3.0.0"}, "devDependencies": {"vite": "workspace:*"}}