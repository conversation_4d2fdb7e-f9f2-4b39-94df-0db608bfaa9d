package main

import (
	"embed"
	"vcloud-db-ui/backend"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
)

//go:embed all:frontend/dist
var assets embed.FS

func main() {
	// Create an instance of the app structure
	app := NewApp()

	// Create VCloudDB service instance
	vcloudDBService := backend.NewVCloudDBService()

	// Create application with options
	err := wails.Run(&options.App{
		Title:  "VCloud DB UI",
		Width:  1200,
		Height: 800,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		BackgroundColour: &options.RGBA{R: 27, G: 38, B: 54, A: 1},
		OnStartup:        app.startup,
		Bind: []interface{}{
			app,
			vcloudDBService, // 绑定 VCloudDBService
		},
	})

	if err != nil {
		println("Error:", err.Error())
	}
}
